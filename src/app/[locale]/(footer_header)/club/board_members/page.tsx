"use client";

import React from "react";
import { useEffect, useState } from "react";
import translations from "@/lib/locales";
import Image from "next/image";
import { fetchApi } from "@/lib/api";
import Modal from "@/components/Modal";
import Link from "next/link";

export default function boardMembersPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = React.use(params);
  const t = (key: string) => translations[locale]?.[key] || key;
  const [board_members, setboardMembers] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMember, setModalMember] = useState<any | null>(null);
  const [modalLoading, setModalLoading] = useState(false);
  const [modalError, setModalError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchboardMembers() {
      setLoading(true);
      setError(null);
      try {
        const data = await fetchApi("/api/club/board-members", { headers: { "X-localization": locale } });
        if (data.status && Array.isArray(data.data)) {
          setboardMembers(data.data);
        } else {
          setError(t("no_board_members"));
        }
      } catch (err) {
        setError(t("no_board_members"));
      } finally {
        setLoading(false);
      }
    }
    fetchboardMembers();
  }, [locale]);

  async function handleCardClick(slug: string) {
    setModalOpen(true);
    setModalLoading(true);
    setModalError(null);
    setModalMember(null);
    try {
      const data = await fetchApi(`/api/club/board-members/${slug}`, { headers: { "X-localization": locale } });
      if (data.status && data.data) {
        setModalMember(data.data);
      } else {
        setModalError(t("no_board_members"));
      }
    } catch (err) {
      setModalError(t("no_board_members"));
    } finally {
      setModalLoading(false);
    }
  }

  return (
    <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-12 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            <Link href={`/${locale}/club`} className="hover:underline text-[#C60428] font-bold">{t('club') || 'النادي'}</Link>
          </li>
          <li className="mx-1">|</li>
          <li>
            {t("board_members") || "مجلس الأدارة"}
          </li>
        </ol>
      </nav>
      {/* End Breadcrumb */}
      {/* Header with stadium image and overlay title */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src="/img/club_bg.png"
          alt={t("club_board_members") || "مجلس إدارة النادى الأهلى"}
          fill
          className="object-cover w-full h-full"
          priority
        />
        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {(t("club_board_members") || "مجلس إدارة النادى الأهلى").split('\n').map((line: string, idx: number, arr: string[]) => (
              <React.Fragment key={idx}>
                {line}
                {idx !== arr.length - 1 && <br />}
              </React.Fragment>
            ))}
          </h2>
        </div>
      </div>
      {/* boardMembers grid */}
      <section className="max-w-6xl mx-auto w-full py-12 px-4">
        {loading ? (
          <div className="text-center text-[#C60428] py-10 text-lg font-bold">{t("loading")}</div>
        ) : error ? (
          <div className="text-center text-red-600 py-10 text-lg font-bold">{error}</div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10">
            {board_members.map((member) => {
              return (
                <button
                  key={member.id}
                  onClick={() => handleCardClick(member.slug)}
                  className="flex flex-col justify-between items-center bg-white rounded-2xl shadow-md min-h-[300px] h-full overflow-hidden p-0 focus:outline-none focus:ring-2 focus:ring-[#C60428]"
                  style={{ cursor: 'pointer' }}
                >
                  <div className="w-full h-full flex flex-col items-center">
                    <div className="relative w-full h-full bg-[#f3f3f3] shadow border-2 border-[#eee] overflow-hidden">
                      <Image
                        src={member.image}
                        alt={member.name}
                        fill
                        className="object-cover object-center w-full h-full absolute inset-x-0 top-0"
                        style={{ objectPosition: 'center' }}
                      />
                    </div>
                  </div>
                  <div className="w-full relative">
                    <div className="absolute start-0 top-0 h-1 w-8 bg-[#C60428] rounded-tr-md rounded-br-md" />
                    <div className="bg-[#222] text-[#e6c97b] text-center font-bold text-base md:text-lg py-3 px-2 rounded-b-2xl flex flex-col items-center justify-center min-h-[56px] gap-0">
                      <span className="text-xl truncate w-full" title={member.name}>{member.name}</span>
                      <span className="text-xs text-white font-normal truncate w-full" title={member.position}>{member.position}</span>
                    </div>
                  </div>
                </button>
              );
            })}
          </div>
        )}
      </section>
      {/* Modal for board member details */}
      {modalOpen && (
        <Modal onClose={() => setModalOpen(false)}>
          <div className={`flex flex-col md:flex-row-reverse gap-0 w-[90vw] max-w-4xl h-[500px]${locale === 'ar' ? ' md:flex-row' : ''}`}>
            {/* Bio and name  */}
            <div className={`flex-1 bg-[#222] p-8 flex flex-col justify-start min-w-0 h-full overflow-y-scroll rounded-e-2xl custom-scrollbar order-1 md:order-2${locale === 'ar' ? ' md:order-1 rounded-e-2xl md:rounded-s-2xl' : ''}`}>
              {modalLoading ? (
                <div className="text-center text-[#C60428] py-10 text-lg font-bold">{t("loading")}</div>
              ) : modalError ? (
                <div className="text-center text-red-600 py-10 text-lg font-bold">{modalError}</div>
              ) : modalMember ? (
                <>
                  <h2 className="text-3xl font-bold text-[#e6c97b] mb-2 text-start">{modalMember.name}</h2>
                  <div className="text-[#e6c97b] text-base font-semibold mb-4 text-start">{modalMember.position}</div>
                  <div className="flex flex-col gap-2 overflow-auto max-h-[340px] md:max-h-[400px] [&::-webkit-scrollbar]:w-2 [&::-webkit-scrollbar-track]:bg-white [&::-webkit-scrollbar-thumb]:bg-[#EC2028] text-white text-base leading-8 text-start whitespace-pre-line pe-3 md:pe-4">
                    {modalMember.bio}
                  </div>
                </>
              ) : null}
            </div>
            {/* Left: Image (now on the start) */}
            <div className={`w-full md:w-[350px] flex-shrink-0 bg-[#e5e5e5] flex items-center justify-center h-full rounded-s-2xl order-2 md:order-1${locale === 'ar' ? ' md:order-2 rounded-s-2xl md:rounded-e-2xl' : ''} hidden md:flex`}>
              {modalMember && (
                <Image
                  src={modalMember.image}
                  alt={modalMember.name}
                  width={220}
                  height={220}
                  className="rounded-full object-cover object-center border-4 border-[#fff] shadow w-[180px] h-[180px] md:w-[220px] md:h-[220px]"
                  style={{ background: '#ddd', objectFit: 'cover', objectPosition: 'center' }}
                />
              )}
              {!modalMember && (
                <div className="w-[220px] h-[220px] rounded-full bg-[#ddd] flex items-center justify-center">
                  <svg width="100" height="100" fill="#bbb" viewBox="0 0 24 24"><circle cx="12" cy="8" r="4"/><path d="M12 14c-4.418 0-8 1.79-8 4v2h16v-2c0-2.21-3.582-4-8-4z"/></svg>
                </div>
              )}
            </div>
          </div>
        </Modal>
      )}
    </main>
  );
}
