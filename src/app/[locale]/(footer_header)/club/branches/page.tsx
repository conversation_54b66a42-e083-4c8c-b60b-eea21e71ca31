"use client";

import React from "react";
import { useEffect, useState } from "react";
import translations from "@/lib/locales";
import Image from "next/image";
import { fetchApi } from "@/lib/api";
import Link from "next/link";

export default function BranchesPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = React.use(params);
  const t = (key: string) => translations[locale]?.[key] || key;
  const [branches, setBranches] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchBranches() {
      setLoading(true);
      setError(null);
      try {
        const data = await fetchApi("/api/club/branches", { headers: { "X-localization": locale } });
        if (data.data && Array.isArray(data.data)) {
          setBranches(data.data);
        } else {
          setError(t("no_branches"));
        }
      } catch (err) {
        setError(t("no_branches"));
      } finally {
        setLoading(false);
      }
    }
    fetchBranches();
  }, [locale]);

  return (
    <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-12 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            <Link href={`/${locale}/club`} className="hover:underline text-[#C60428] font-bold">{t('club') || 'النادي'}</Link>
          </li>
          <li className="mx-1">|</li>
          <li>
            {t("branches_and_services") || "الفروع والخدمات"}
          </li>
        </ol>
      </nav>
      {/* End Breadcrumb */}
      {/* Header with stadium image and overlay title */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src="/img/default_bg.png"
          alt={t("club_branches") || "فروع وخدمات النادى"}
          fill
          className="object-cover w-full h-full"
          priority
        />
        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {(t("club_branches") || "فروع وخدمات النادى").split('\n').map((line: string, idx: number, arr: string[]) => (
              <React.Fragment key={idx}>
                {line}
                {idx !== arr.length - 1 && <br />}
              </React.Fragment>
            ))}
          </h2>
        </div>
      </div>
      {/* branches grid */}
      <section className="max-w-6xl mx-auto w-full py-12 px-4">
        {loading ? (
          <div className="text-center text-[#C60428] py-10 text-lg font-bold">{t("loading")}</div>
        ) : error ? (
          <div className="text-center text-red-600 py-10 text-lg font-bold">{error}</div>
        ) : (
          <div className="flex flex-col gap-16">
            {branches.map((branch) => (
              <div key={branch.id} className="bg-white rounded-2xl shadow-md overflow-hidden flex flex-col md:flex-row gap-8 p-6">
                {/* Branch Image */}
                <div className="flex-shrink-0 w-full md:w-1/3 flex items-center justify-center">
                  {branch.image ? (
                    <div className="relative w-full h-48 md:h-56 bg-[#f3f3f3] border-2 border-[#eee] overflow-hidden rounded-xl flex flex-col items-center justify-center">
                      <Image
                        src={branch.image}
                        alt={branch.alt_text || branch.title}
                        fill
                        className="object-cover w-full h-full"
                        style={{ objectPosition: 'top' }}
                      />
                      <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-white/80 px-3 py-1 rounded text-center text-[#C60428] font-bold text-base w-fit max-w-full truncate">
                        {branch.title}
                      </div>
                    </div>
                  ) : (
                    <div className="w-full h-48 md:h-56 bg-[#f3f3f3] border-2 border-[#eee] flex items-center justify-center rounded-xl">
                      <svg width="48" height="48" fill="#bbb" viewBox="0 0 24 24"><path d="M21 19V5a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2ZM5 5h14v7.586l-2.293-2.293a1 1 0 0 0-1.414 0L7 19H5V5Zm14 14H7.414l7.293-7.293a1 1 0 0 1 1.414 0L19 15.586V19Z" /></svg>
                    </div>
                  )}
                </div>
                {/* Branch Content */}
                <div className="flex-1 flex flex-col gap-2">
                  <div className="flex items-center gap-2">
                    <span className="h-2 w-2 rounded-full bg-[#C60428] inline-block"></span>
                    <h3 className="text-xl font-bold text-[#C60428]">{branch.title}</h3>
                  </div>
                  <div className="text-[#e6c97b] font-semibold text-base">{branch.sub_title}</div>
                  <div className="text-[#222] text-sm leading-relaxed">{branch.description}</div>
                  {/* Services */}
                  {branch.services && branch.services.length > 0 && (
                    <div>
                      <div className="font-bold text-[#C60428] mb-2">{t("branch_services") || "خدمات فرع النادي"}</div>
                      <ul className="list-disc list-inside grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-x-8 gap-y-1 text-[#222] text-sm">
                        {branch.services.map((service: any) => (
                          <li key={service.id}>{service.title}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {/* Branch Images */}
                  {branch.images && branch.images.length > 0 && (
                    <div className="mt-4">
                      <div className="font-bold text-[#C60428] mb-2">{t("branch_gallery") || "مواقع الفرع بالصور"}</div>
                      <div className="flex flex-wrap gap-4 justify-center">
                        {branch.images.map((img: any) => (
                          <div key={img.id} className="flex flex-col items-center w-32">
                            <div className="relative w-32 h-24 bg-[#f3f3f3] border-2 border-[#eee] rounded-lg overflow-hidden">
                              {img.image ? (
                                <Image
                                  src={img.image}
                                  alt={img.title || branch.title}
                                  fill
                                  className="object-cover w-full h-full"
                                  style={{ objectPosition: 'top' }}
                                />
                              ) : null}
                            </div>
                            <div className="text-center text-[#C60428] font-bold text-sm mt-2 w-full break-words">
                              {img.title}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </section>
    </main>
  );
}
