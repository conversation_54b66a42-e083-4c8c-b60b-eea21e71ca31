"use client";

import React, { useEffect, useState } from "react";
import translations from "@/lib/locales";
import Image from "next/image";
import { fetchApi } from "@/lib/api";
import Link from "next/link";

export default function HistorySectionsPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = React.use(params);
  const t = (key: string) => translations[locale]?.[key] || key;
  const [historySections, setHistorySections] = useState<any[]>([]);
  const [timelineEvents, setTimelineEvents] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      setLoading(true);
      setError(null);
      try {
        const [sectionsRes, timelineRes] = await Promise.all([
          fetchApi("/api/club/history/sections", { headers: { "X-localization": locale } }),
          fetchApi("/api/club/history/timeline", { headers: { "X-localization": locale } })
        ]);
        if (sectionsRes.data && Array.isArray(sectionsRes.data)) {
          setHistorySections(sectionsRes.data);
        } else {
          setError(t("no_histories"));
        }
        if (timelineRes.data && Array.isArray(timelineRes.data)) {
          setTimelineEvents(timelineRes.data);
        }
      } catch (err) {
        setError(t("no_histories"));
      } finally {
        setLoading(false);
      }
    }
    fetchData();
  }, [locale]);

  return (
    <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-12 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            <Link href={`/${locale}/club`} className="hover:underline text-[#C60428] font-bold">{t('club') || 'النادي'}</Link>
          </li>
          <li className="mx-1">|</li>
          <li>
            {t("club_history") || "تاريخ النادي الأهلي"}
          </li>
        </ol>
      </nav>
      {/* End Breadcrumb */}
      {/* Header with stadium image and overlay title */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src="/img/default_bg.png"
          alt={t("club_history") || "تاريخ النادي الأهلي"}
          fill
          className="object-cover w-full h-full"
          priority
        />

        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {(t("club_history") || "تاريخ النادي الأهلي").split('\n').map((line: string, idx: number, arr: string[]) => (
              <React.Fragment key={idx}>
                {line}
                {idx !== arr.length - 1 && <br />}
              </React.Fragment>
            ))}
          </h2>
        </div>
      </div>
      {/* History Sections as Cards */}
      <section className="max-w-6xl mx-auto w-full py-12 px-4">
        {loading ? (
          <div className="text-center text-[#C60428] py-10 text-lg font-bold">{t("loading")}</div>
        ) : error ? (
          <div className="text-center text-red-600 py-10 text-lg font-bold">{error}</div>
        ) : (
          <div className="flex flex-col gap-6">
            {historySections.map((section, idx) => (
              <HistoryAccordionSection key={section.id} section={section} t={t} locale={locale} />
            ))}
          </div>
        )}
      </section>
      {/* Timeline Carousel */}
      {timelineEvents.length > 0 && (
        <section className="max-w-6xl mx-auto w-full py-12 px-4">
          <div className="font-bold text-[#C60428] text-xl mb-6 text-center">{t('club_history_timeline') || 'محطات من تاريخ النادى'}</div>
          <TimelineCarousel events={timelineEvents} t={t} locale={locale} />
        </section>
      )}
    </main>
  );
}

// TimelineCarousel component (inline for this file, or move to components/if reused)
function TimelineCarousel({ events, t, locale }: { events: any[], t: (key: string) => string, locale: string }) {
  const [activeIndex, setActiveIndex] = React.useState(Math.floor(events.length / 2));
  const [isAutoPlaying, setIsAutoPlaying] = React.useState(true);
  const [isClient, setIsClient] = React.useState(false);
  const [videoModal, setVideoModal] = React.useState<{ url: string, image: string, title: string } | null>(null);
  const [lightbox, setLightbox] = React.useState<{ image: string, title: string } | null>(null);
  const isRTL = locale === 'ar' || locale === 'he' || locale === 'fa';

  React.useEffect(() => { setIsClient(true); }, []);

  const next = () => {
    setActiveIndex((prev) => (prev + 1) % events.length);
    setIsAutoPlaying(false);
  };
  const prev = () => {
    setActiveIndex((prev) => (prev - 1 + events.length) % events.length);
    setIsAutoPlaying(false);
  };

  const getStyleForEvent = (index: number) => {
    let diff = index - activeIndex;
    const half = Math.floor(events.length / 2);
    if (diff > half) diff -= events.length;
    if (diff < -half) diff += events.length;
    const distance = Math.abs(diff);
    let scale = 1, opacity = 1, zIndex = 30, translateX = 0, fontSize = '1rem';
    if (isClient) {
      translateX = diff * (window.innerWidth >= 768 ? 340 : 180);
      if (index === activeIndex) {
        scale = window.innerWidth >= 768 ? 1.15 : 1.08;
        zIndex = 50;
        opacity = 1;
        translateX = 0;
        fontSize = '1.125rem'; // text-lg
      } else if (distance <= 3) {
        scale = 1 - distance * 0.06;
        opacity = 1 - distance * 0.18;
        zIndex = 30 - distance * 5;
        fontSize = '0.875rem'; // text-sm for unactive
      } else {
        scale = 0.8;
        opacity = 0;
        zIndex = 0;
        fontSize = '0.875rem';
      }
    }
    if (!isClient) {
      scale = 1; opacity = 1; zIndex = 30; translateX = 0; fontSize = '1rem';
    }
    return {
      position: "absolute" as const,
      left: "50%",
      top: "50%",
      transform: `translateX(calc(${translateX}px - 50%)) translateY(-50%) scale(${scale})`,
      opacity,
      zIndex,
      transition: "transform 0.5s ease, opacity 0.5s ease",
      width: isClient && window.innerWidth >= 768 ? "320px" : "220px",
      display: opacity === 0 ? "none" : "flex",
      flexDirection: opacity === 0 ? undefined : "column" as const,
      alignItems: opacity === 0 ? undefined : "center" as const,
      justifyContent: opacity === 0 ? undefined : "flex-end" as const,
      fontSize,
    } as React.CSSProperties;
  };

  React.useEffect(() => {
    if (!isAutoPlaying) return;
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % events.length);
    }, 3500);
    return () => clearInterval(interval);
  }, [isAutoPlaying, events.length]);

  if (!events.length) return null;

  return (
    <div className="w-full relative">
      <div className="relative w-full h-[320px] md:h-[340px] mx-auto mt-4 md:mt-6 overflow-hidden">
        <div className="relative w-full h-full">
          {events.map((event, index) => {
            const styles = getStyleForEvent(index);
            const isActive = index === activeIndex;
            return (
              <div key={event.id + "-timeline"} style={styles}>
                <div className="w-full h-40 relative mb-4 rounded-lg overflow-hidden group">
                  {event.video_url ? (
                    <>
                      <Image src={event.image} alt={event.event} fill className="object-cover w-full h-full" />
                      <button
                        className="absolute inset-0 flex items-center justify-center bg-black/40 hover:bg-black/60 transition z-10 w-full h-full cursor-pointer"
                        onClick={() => setVideoModal({ url: event.video_url, image: event.image, title: event.event })}
                        aria-label={t('watch_now') || 'شاهد الآن'}
                        type="button"
                      >
                        <span className="bg-[#EC2028] text-white rounded-full w-20 h-20 flex items-center justify-center text-4xl shadow-lg border-4 border-white/80 group-hover:scale-110 transition-transform duration-200">
                          ▶
                        </span>
                      </button>
                    </>
                  ) : (
                    <button
                      className="absolute inset-0 w-full h-full z-10 cursor-zoom-in"
                      onClick={() => setLightbox({ image: event.image, title: event.event })}
                      aria-label={t('view_image') || 'عرض الصورة'}
                      type="button"
                      style={{ background: 'transparent', padding: 0, border: 'none' }}
                    >
                      <Image src={event.image} alt={event.event} fill className="object-cover w-full h-full cursor-zoom-in select-none pointer-events-none" draggable={false} onContextMenu={e => e.preventDefault()} />
                    </button>
                  )}
                </div>
                <div className="text-[#C60428] font-bold text-lg mb-1">{event.year}</div>
                <div className={`text-[#222] text-center ${isActive ? 'text-base font-bold' : 'text-xs font-normal opacity-70'}`}>{event.event}</div>
              </div>
            );
          })}
        </div>
        {/* Move navigation arrows to the very bottom, full width, centered */}
        <div className="absolute left-0 right-0 bottom-0 flex justify-between items-end w-full px-[10%] md:px-[25%] pt-12 pointer-events-none z-50">
          <button
            onClick={isRTL ? next : prev}
            className="cursor-pointer text-[#C60428] bg-white/80 px-3 py-1 md:px-4 md:py-2 rounded-lg hover:bg-[#C60428] hover:text-white transition-colors duration-300 text-3xl pointer-events-auto"
            style={{ minWidth: 56 }}
          >
            {isRTL ? '❯' : '❮'}
          </button>
          <button
            onClick={isRTL ? prev : next}
            className="cursor-pointer text-[#C60428] bg-white/80 px-3 py-1 md:px-4 md:py-2 rounded-lg hover:bg-[#C60428] hover:text-white transition-colors duration-300 text-3xl pointer-events-auto"
            style={{ minWidth: 56 }}
          >
            {isRTL ? '❮' : '❯'}
          </button>
        </div>
        {videoModal && (
          <div
            className="fixed inset-0 z-[1000002] flex items-center justify-center bg-black bg-opacity-80 top-0 left-0 w-screen h-screen"
            onClick={() => setVideoModal(null)}
            tabIndex={0}
          >
            <div
              className="relative w-[90vw] max-w-3xl h-[60vw] max-h-[70vh] bg-black rounded-lg flex flex-col items-center justify-center"
              onClick={e => e.stopPropagation()}
            >
              <button
                className="absolute top-2 right-2 text-white text-2xl bg-black bg-opacity-60 rounded-full px-3 py-1 hover:bg-opacity-90"
                onClick={() => setVideoModal(null)}
                aria-label="Close"
                type="button"
              >
                ×
              </button>
              <iframe
                width="100%"
                height="100%"
                src={videoModal.url.includes('youtube.com/embed') ? `${videoModal.url}?autoplay=1&rel=0` : videoModal.url}
                title="YouTube video player"
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
                className="rounded-lg w-full h-full"
              ></iframe>
            </div>
          </div>
        )}
        {lightbox && (
          <div
            className="fixed inset-0 z-[1000002] flex items-center justify-center bg-black/60 top-0 left-0 w-screen h-screen cursor-pointer"
            onClick={() => setLightbox(null)}
            tabIndex={-1}
            ref={el => { if (el) el.focus(); }}
            onKeyDown={e => {
              if (e.key === 'Escape') setLightbox(null);
            }}
            role="dialog"
            aria-modal="true"
          >
            <div
              className="relative w-[90vw] max-w-2xl h-auto max-h-[80vh] bg-black rounded-lg flex flex-col items-center justify-center p-4 cursor-default"
              onClick={e => e.stopPropagation()}
            >
              <button
                className="absolute top-2 right-2 text-white text-2xl bg-black bg-opacity-60 rounded-full px-3 py-1 hover:bg-opacity-90 cursor-pointer z-[60]"
                onClick={e => { e.stopPropagation(); setLightbox(null); }}
                aria-label="Close"
                type="button"
                tabIndex={0}
              >
                ×
              </button>
              <div className="w-full flex flex-col items-center">
                <div className="relative w-full h-64 md:h-96 mb-4">
                  <Image src={lightbox.image} alt={lightbox.title} fill className="object-contain rounded-lg w-full h-full" draggable={false} onContextMenu={e => e.preventDefault()} />
                </div>
                <div className="text-white text-lg font-bold text-center mt-2">{lightbox.title}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

function HistoryAccordionSection({ section, t, locale }: { section: any, t: (key: string) => string, locale: string }) {
  const [open, setOpen] = React.useState(false);
  const [details, setDetails] = React.useState<any>(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);

  const handleToggle = async () => {
    setOpen((prev) => !prev);
    if (!open && !details) {
      setLoading(true);
      setError(null);
      try {
        const data = await fetchApi(`/api/club/history/sections/${section.id}`, { headers: { "X-localization": locale } });
        if (data.data) {
          setDetails(data.data);
        } else {
          setError(t("no_histories"));
        }
      } catch (err) {
        setError(t("no_histories"));
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="bg-white rounded-xl shadow overflow-hidden">
      <button
        className="w-full flex items-center justify-between p-6 focus:outline-none hover:bg-gray-50 transition-colors"
        onClick={handleToggle}
        aria-expanded={open}
      >
        <div className="flex items-center gap-4">
          <div className="w-20 h-20 relative rounded-lg overflow-hidden flex-shrink-0">
            <Image src={section.image} alt={section.title} fill className="object-cover w-full h-full select-none pointer-events-none" draggable={false} onContextMenu={e => e.preventDefault()} />
          </div>
          <h3 className="text-xl font-bold text-[#C60428]">{section.title}</h3>
        </div>
        <span className={`transition-transform duration-300 text-2xl ${open ? 'rotate-180' : ''}`}>⌄</span>
      </button>
      {open && (
        <div className="border-t px-6 pb-6 pt-2 animate-fade-in">
          {loading ? (
            <div className="text-[#C60428] py-4">{t('loading')}</div>
          ) : error ? (
            <div className="text-red-600 py-4">{error}</div>
          ) : details ? (
            <React.Fragment>
              {details.cover && (
                <div className="w-full h-56 md:h-80 relative mb-6 rounded-lg overflow-hidden">
                  <Image src={details.cover} alt={details.title} fill className="object-cover w-full h-full select-none pointer-events-none" draggable={false} onContextMenu={e => e.preventDefault()} />
                </div>
              )}
              <div className="text-[#222] text-sm md:text-base leading-relaxed mb-4" dangerouslySetInnerHTML={{ __html: details.content }} />
              {details.images && details.images.length > 0 && (
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                  {details.images.map((img: string, idx: number) => (
                    <div key={idx} className="w-full h-32 sm:h-40 relative rounded-lg overflow-hidden group">
                      <Image src={img} alt={`History image ${idx + 1}`} fill className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105" />
                    </div>
                  ))}
                </div>
              )}
            </React.Fragment>
          ) : null}
        </div>
      )}
    </div>
  );
}
