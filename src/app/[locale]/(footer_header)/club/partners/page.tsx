"use client";

import React from "react";
import { useEffect, useState } from "react";
import translations from "@/lib/locales";
import Image from "next/image";
import { fetchApi } from "@/lib/api";
import Link from "next/link";

export default function PartnersPage({ params }: { params: Promise<{ locale: string }> }) {
  const { locale } = React.use(params);
  const t = (key: string) => translations[locale]?.[key] || key;
  const [partners, setPartners] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchPartners() {
      setLoading(true);
      setError(null);
      try {
        const data = await fetchApi("/api/club/partners", { headers: { "X-localization": locale } });
        if (data.status && Array.isArray(data.data)) {
          setPartners(data.data);
        } else {
          setError(t("no_partners"));
        }
      } catch (err) {
        setError(t("no_partners"));
      } finally {
        setLoading(false);
      }
    }
    fetchPartners();
  }, [locale]);

  return (
    <main className="flex flex-col bg-[#f6f6f6] min-h-fit pt-24 pb-12 lg:pt-[175px]">
      {/* Breadcrumb */}
      <nav className="w-full container mx-auto px-2 md:px-4 mt-1 mb-2 text-sm text-gray-600" aria-label="breadcrumb">
        <ol className="flex flex-wrap items-center gap-1">
          <li>
            <Link href={`/${locale}/club`} className="hover:underline text-[#C60428] font-bold">{t('club') || 'النادي'}</Link>
          </li>
          <li className="mx-1">|</li>
          <li>
            {t("partners") || "الشركاء"}
          </li>
        </ol>
      </nav>
      {/* End Breadcrumb */}
      {/* Header with stadium image and overlay title */}
      <div className="relative w-full h-64 md:h-96 flex items-center justify-center">
        <Image
          src="/img/default_bg.png"
          alt="stadium"
          fill
          className="object-cover w-full h-full"
          priority
        />
        <div className="absolute inset-0 bg-black/40 flex flex-col items-start z-10 px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white drop-shadow-lg mt-16 ms-2 ms:md-24" style={{ textShadow: '0 1px 5px #dba527' }}>
            {t("partners") || "شركاء النادي"}
          </h2>
        </div>
      </div>
      {/* Partners grid */}
      <section className="max-w-6xl mx-auto w-full py-12 px-4">
        {loading ? (
          <div className="text-center text-[#C60428] py-10 text-lg font-bold">{t("loading")}</div>
        ) : error ? (
          <div className="text-center text-red-600 py-10 text-lg font-bold">{error}</div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-10">
            {partners.map((partner) => {
              const hasAnySocial = partner.website || (partner.social && (
                partner.social.facebook || partner.social.instagram || partner.social.x)
              );
              return (
                <div
                  key={partner.id}
                  className="flex flex-col items-center rounded-2xl py-8 px-4 min-h-[260px]"
                >
                  <div className="w-32 h-32 rounded-full bg-[#f3f3f3] flex items-center justify-center shadow mb-4 border-2 border-[#eee]">
                    <Image
                      src={partner.logo}
                      alt={partner.name}
                      width={150}
                      height={150}
                      className="object-contain w-24 h-24"
                    />
                  </div>
                  <div className="text-base md:text-lg font-bold text-[#222] text-center mb-2 min-h-[32px] flex items-center justify-center">
                    {partner.name.charAt(0).toUpperCase() + partner.name.slice(1)}
                  </div>
                  {hasAnySocial && (
                    <div className="flex gap-4 mt-2 justify-center">
                      {(partner.website) && (
                        <a href={partner.website} target="_blank" rel="noopener noreferrer"
                          className="rounded-full bg-[#272525] flex items-center justify-center w-10 h-10">
                          <Image src="/socials/Website-icon.png" alt="Website" width={24} height={24} />
                        </a>
                      )}
                      {partner.social?.facebook && (
                        <a href={partner.social.facebook} target="_blank" rel="noopener noreferrer"
                          className="rounded-full bg-[#272525] flex items-center justify-center w-10 h-10">
                          <Image src="/socials/Facebook-icon.png" alt="Facebook" width={24} height={24} />
                        </a>
                      )}
                      {partner.social?.instagram && (
                        <a href={partner.social.instagram} target="_blank" rel="noopener noreferrer"
                          className="rounded-full bg-[#272525] flex items-center justify-center w-10 h-10">
                          <Image src="/socials/Instagram-icon.png" alt="Instagram" width={24} height={24} />
                        </a>
                      )}
                      {partner.social?.x && (
                        <a href={partner.social.x} target="_blank" rel="noopener noreferrer"
                          className="rounded-full bg-[#272525] flex items-center justify-center w-10 h-10">
                          <Image src="/socials/X-icon.png" alt="X" width={24} height={24} />
                        </a>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </section>
    </main>
  );
}
