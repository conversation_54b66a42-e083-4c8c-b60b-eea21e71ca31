"use client";

import React, { useEffect, useRef, useState } from 'react';
import { pdfjs, Document, Page } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import { usePathname } from "next/navigation";
import translations from "@/lib/locales";

// Configure PDF.js worker
pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';

interface ProtectedPdfViewerProps {
  pdfUrl: string;
}

function useTranslations() {
  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";
  return (key: string) => translations[locale]?.[key] || key;
}

const ProtectedPdfViewer: React.FC<ProtectedPdfViewerProps> = ({ pdfUrl }) => {
  const t = useTranslations();
  const containerRef = useRef<HTMLDivElement>(null);
  const [numPages, setNumPages] = useState<number>(0);
  const [scale, setScale] = useState<number>(1.0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pageWidth, setPageWidth] = useState<number | undefined>(undefined);

  function onDocumentLoadSuccess({ numPages }: { numPages: number }) {
    setNumPages(numPages);
    setLoading(false);
    console.log(`PDF loaded successfully with ${numPages} pages`);
  }

  function onDocumentLoadError(error: Error) {
    console.error('PDF loading error:', error);
    setError(t('magazine_load_error') || 'حدث خطأ أثناء تحميل المجلة. يرجى المحاولة مرة أخرى.');
    setLoading(false);
  }



  function zoomIn() {
    setScale(prevScale => Math.min(prevScale + 0.2, 3.0));
  }

  function zoomOut() {
    setScale(prevScale => Math.max(prevScale - 0.2, 0.5));
  }

  // Calculate responsive page width
  useEffect(() => {
    const updatePageWidth = () => {
      const containerWidth = containerRef.current?.clientWidth || window.innerWidth;
      const isMobile = window.innerWidth <= 768;
      if (isMobile) {
        setPageWidth(containerWidth - 32); // Account for padding
      } else {
        setPageWidth(undefined); // Let PDF.js determine width
      }
    };

    updatePageWidth();
    window.addEventListener('resize', updatePageWidth);
    return () => window.removeEventListener('resize', updatePageWidth);
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Disable right-click context menu
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      return false;
    };

    // Disable keyboard shortcuts for saving/printing/dev tools
    const handleKeyDown = (e: KeyboardEvent) => {
      // Disable Ctrl+S (Save), Ctrl+P (Print), F12 (DevTools), Ctrl+Shift+I (DevTools)
      if (
        (e.ctrlKey && (e.key === 's' || e.key === 'S')) ||
        (e.ctrlKey && (e.key === 'p' || e.key === 'P')) ||
        e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && (e.key === 'I' || e.key === 'i')) ||
        (e.ctrlKey && e.shiftKey && (e.key === 'J' || e.key === 'j')) ||
        (e.ctrlKey && (e.key === 'u' || e.key === 'U')) || // View source
        (e.ctrlKey && (e.key === 'a' || e.key === 'A')) || // Select all
        (e.ctrlKey && (e.key === 'c' || e.key === 'C')) || // Copy
        (e.ctrlKey && (e.key === 'x' || e.key === 'X')) || // Cut
        (e.ctrlKey && (e.key === 'v' || e.key === 'V'))    // Paste
      ) {
        e.preventDefault();
        return false;
      }
    };

    // Disable text selection and dragging
    const handleSelectStart = (e: Event) => {
      e.preventDefault();
      return false;
    };

    const handleDragStart = (e: DragEvent) => {
      e.preventDefault();
      return false;
    };

    // Add event listeners
    container.addEventListener('contextmenu', handleContextMenu);
    container.addEventListener('selectstart', handleSelectStart);
    container.addEventListener('dragstart', handleDragStart);
    document.addEventListener('keydown', handleKeyDown);

    // Cleanup
    return () => {
      container.removeEventListener('contextmenu', handleContextMenu);
      container.removeEventListener('selectstart', handleSelectStart);
      container.removeEventListener('dragstart', handleDragStart);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className="protected-pdf-viewer"
      style={{
        height: '100vh',
        width: '100%',
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none',
        WebkitTouchCallout: 'none',
        position: 'relative',
        display: 'flex',
        flexDirection: 'column'
      } as React.CSSProperties}
    >
      {/* Protection Notice */}
      <div
        className="absolute top-2 left-2 sm:top-3 sm:left-3 bg-red-600 bg-opacity-90 text-white px-2 sm:px-3 py-1 sm:py-2 rounded text-xs sm:text-sm font-bold z-50"
      >
        🔒 {t('protected_notice') || 'محمي من التحميل والطباعة'}
      </div>
      {loading && !error && (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-[#C60428] py-10 text-lg font-bold">
            {t('loading_magazine') || 'جاري تحميل المجلة...'}
          </div>
        </div>
      )}

      {error && (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-red-600 py-10 text-lg font-bold">
            {error}
          </div>
        </div>
      )}

      <Document
        file={pdfUrl}
        onLoadSuccess={onDocumentLoadSuccess}
        onLoadError={onDocumentLoadError}
        loading={null}
        className="flex flex-col flex-1"
      >
        {!loading && !error && (
          <>
            {/* Custom Toolbar */}
            <div className="w-full bg-gray-100 border-b border-gray-300 p-2 sm:p-3 flex flex-col sm:flex-row items-center justify-center gap-2">
              <div className="flex items-center gap-1 sm:gap-2">
                <button
                  onClick={zoomOut}
                  disabled={scale <= 0.5}
                  className="px-2 sm:px-3 py-1 bg-[#C60428] text-white rounded disabled:bg-gray-400 disabled:cursor-not-allowed text-xs sm:text-sm"
                >
                  تصغير
                </button>
                <span className="mx-1 sm:mx-2 text-xs sm:text-sm font-medium">{Math.round(scale * 100)}%</span>
                <button
                  onClick={zoomIn}
                  disabled={scale >= 3.0}
                  className="px-2 sm:px-3 py-1 bg-[#C60428] text-white rounded disabled:bg-gray-400 disabled:cursor-not-allowed text-xs sm:text-sm"
                >
                  تكبير
                </button>
              </div>
            </div>

            {/* All PDF Pages - Scrollable */}
            <div
              className="flex-1 overflow-auto p-1 sm:p-4 bg-gray-50"
              style={{
                height: 'calc(100vh - 100px)',
                overflowY: 'auto',
                overflowX: 'hidden'
              }}
            >
              <div className="flex flex-col items-center gap-2 sm:gap-4 w-full">
                {Array.from(new Array(numPages), (el, index) => (
                  <div key={`page_${index + 1}`} className="w-full max-w-full shadow-lg bg-white rounded overflow-hidden flex justify-center">
                    <Page
                      pageNumber={index + 1}
                      scale={scale}
                      renderAnnotationLayer={false}
                      renderTextLayer={false}
                      className="max-w-full"
                      width={pageWidth}
                    />
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </Document>
      
      {/* Additional CSS to prevent interactions */}
      <style jsx>{`
        .protected-pdf-viewer :global(.react-pdf__Page__textContent) {
          user-select: none !important;
          -webkit-user-select: none !important;
          -moz-user-select: none !important;
          -ms-user-select: none !important;
        }

        .protected-pdf-viewer :global(.react-pdf__Page__canvas) {
          -webkit-user-drag: none !important;
          -khtml-user-drag: none !important;
          -moz-user-drag: none !important;
          -o-user-drag: none !important;
          user-drag: none !important;
          pointer-events: auto !important;
        }

        /* Hide browser's built-in PDF controls */
        .protected-pdf-viewer :global(embed),
        .protected-pdf-viewer :global(object) {
          pointer-events: none;
        }

        /* Prevent image saving */
        .protected-pdf-viewer :global(img) {
          -webkit-user-drag: none !important;
          -khtml-user-drag: none !important;
          -moz-user-drag: none !important;
          -o-user-drag: none !important;
          user-drag: none !important;
        }

        /* Prevent canvas context menu but allow scrolling */
        .protected-pdf-viewer :global(canvas) {
          -webkit-user-drag: none !important;
          -khtml-user-drag: none !important;
          -moz-user-drag: none !important;
          -o-user-drag: none !important;
          user-drag: none !important;
          pointer-events: auto !important;
        }

        /* Ensure scrolling works */
        .protected-pdf-viewer :global(.react-pdf__Document) {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .protected-pdf-viewer :global(.react-pdf__Page) {
          margin: 0 auto;
        }
      `}</style>
    </div>
  );
};

export default ProtectedPdfViewer;
