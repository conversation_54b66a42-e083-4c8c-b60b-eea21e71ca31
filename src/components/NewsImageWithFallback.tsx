"use client";
import Image from "next/image";
import { useState } from "react";

interface NewsImageWithFallbackProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  fill?: boolean;
  className?: string;
  sizes?: string;
  style?: React.CSSProperties;
  priority?: boolean;
  quality?: number;
  placeholder?: "blur" | "empty";
  blurDataURL?: string;
  onError?: () => void;
  onLoad?: () => void;
  onLoadingComplete?: () => void;
  responsive?: boolean; // New prop to enable responsive behavior
  aspectRatio?: "video" | "square" | "portrait" | "auto"; // New prop for aspect ratio
  [key: string]: any;
}

export default function NewsImageWithFallback({
  src,
  alt,
  responsive = false,
  aspectRatio = "auto",
  className = "",
  sizes,
  style,
  onError,
  ...props
}: NewsImageWithFallbackProps) {
  const [imgSrc, setImgSrc] = useState(src || "/img/default_placeholder.png");

  const handleError = () => {
    setImgSrc("/img/default_placeholder.png");
    if (onError) onError();
  };

  // Define aspect ratio classes
  const aspectRatioClasses = {
    video: "aspect-video", // 16:9
    square: "aspect-square", // 1:1
    portrait: "aspect-[3/4]", // 3:4
    auto: ""
  };

  // If responsive is enabled, wrap in a responsive container
  if (responsive) {
    const containerClasses = `
      w-full
      ${aspectRatioClasses[aspectRatio]}
      relative
      rounded
      overflow-hidden
    `.trim().replace(/\s+/g, ' ');

    const imageClasses = `
      object-cover
      ${className}
    `.trim();

    const responsiveSizes = sizes ||
      "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw";

    return (
      <div className={containerClasses}>
        <Image
          {...props}
          src={imgSrc}
          alt={alt}
          fill
          sizes={responsiveSizes}
          className={imageClasses}
          style={style}
          onError={handleError}
        />
      </div>
    );
  }

  // Default behavior (backward compatibility)
  return (
    <Image
      {...props}
      src={imgSrc}
      alt={alt}
      className={className}
      sizes={sizes}
      style={style}
      onError={handleError}
    />
  );
}
