"use client";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";
import { usePathname } from "next/navigation";
import translations from "@/lib/locales";

function useTranslations() {
  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";
  return (key: string) => translations[locale]?.[key] || key;
}

export interface HeroNewsItem {
  src: string;
  title: string;
  slug: string;
  intro: string;
}

interface HeroSectionProps {
  news: HeroNewsItem[];
  matches?: any[];
}

export default function HeroSection({ news, matches = [] }: HeroSectionProps) {
  const t = useTranslations();
  const articles = news && news.length > 0 ? news : [];
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  const pathname = usePathname();
  const locale = pathname ? pathname.split("/")[1] : "ar";

  const next = () => {
    setActiveIndex((prev) => (prev + 1) % articles.length);
    setIsAutoPlaying(false);
  };

  const prev = () => {
    setActiveIndex((prev) => (prev - 1 + articles.length) % articles.length);
    setIsAutoPlaying(false);
  };

  // Helper to determine direction for carousel
  const isRTL = locale === 'ar' || locale === 'ar-EG' || locale.startsWith('ar');

  const getStyleForPlayer = (index: number) => {
    let diff = index - activeIndex;
    const half = Math.floor(articles.length / 2);

    if (diff > half - 1) diff -= articles.length;
    if (diff < -half) diff += articles.length;

    const distance = Math.abs(diff);

    let scale = 1;
    let opacity = 1;
    let zIndex = 30;
    // Fix: Use direction-aware translateX, no static offset
    let translateX = (isRTL ? -diff : diff) * 300;

    if (index === activeIndex) {
      zIndex = 50;
      translateX = 0;
    } else {
      zIndex = 30 - distance * 5;
    }

    return {
      position: "absolute",
      [isRTL ? 'right' : 'left']: 0,
      bottom: 50,
      transform: `translateX(${translateX}px) scale(${scale})`,
      opacity,
      zIndex,
      transition: "transform 0.5s cubic-bezier(0.77,0,0.18,1), opacity 0.5s ease",
      transformOrigin: "center center",
      display: "flex",
      flexDirection: "column" as const,
      alignItems: "center" as const,
      justifyContent: "flex-end" as const,
    } as React.CSSProperties;
  };

  const containerRef = useRef<HTMLDivElement>(null);
  const matchesContainerRef = useRef<HTMLDivElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const handleMouseDown = (e: React.MouseEvent) => {
    const container = containerRef.current;
    if (!container) return;
    setIsDragging(true);
    setStartX(e.pageX - container.offsetLeft);
    setScrollLeft(container.scrollLeft);
  };
  const handleMouseMove = (e: React.MouseEvent) => {
    const container = containerRef.current;
    if (!container || !isDragging) return;
    e.preventDefault(); // prevent text selection
    const x = e.pageX - container.offsetLeft;
    const walk = (x - startX) * 1; // 1 = scroll speed multiplier
    container.scrollLeft = scrollLeft - walk;
  };
  const handleMouseUp = () => setIsDragging(false);
  const handleMouseLeave = () => setIsDragging(false);

  useEffect(() => {
    if (!isAutoPlaying || articles.length === 0) return;
    const interval = setInterval(() => {
      setActiveIndex((prev) => (prev + 1) % articles.length);
    }, 6000); // Changed from 3000 to 6000 for slower rotation

    return () => clearInterval(interval);
  }, [isAutoPlaying, articles.length]);

  // Helper to format date in Arabic, English, and French using toLocaleDateString
  function formatMatchDate(dateStr: string, locale: string) {
    if (!dateStr) return '';
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    return date.toLocaleDateString(locale.replace('_', '-'), {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    });
  }

  // Helper to format time in 12-hour format and get period (AM/PM) with translation
  function formatMatchTime(timeStr: string, locale: string) {
    if (!timeStr) return { time: '', period: '' };
    // Accepts 'HH:mm:ss' or 'HH:mm' format
    const [h, m] = timeStr.split(":");
    let hour = parseInt(h, 10);
    const minute = m ? m.padStart(2, '0') : '00';
    const isAM = hour < 12;
    let displayHour = hour % 12;
    if (displayHour === 0) displayHour = 12;
    const time = `${displayHour}:${minute}`;
    // Get translation for period
    let periodKey = isAM ? 'morning' : 'evening';
    let period = translations[locale]?.[periodKey] || (isAM ? 'صباحا' : 'مساء');
    return { time, period };
  }

  // Helper to capitalize the first letter of a string (locale-aware)
  function capitalizeFirst(str: string, locale: string) {
    if (!str) return '';
    return str.charAt(0).toLocaleUpperCase(locale) + str.slice(1);
  }

  // --- Add handlers for wheel and touch events to change slide on scroll by hand ---
  const touchStartX = useRef<number | null>(null);
  const touchEndX = useRef<number | null>(null);

  // Touch events for mobile
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
  };
  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.touches[0].clientX;
  };
  const handleTouchEnd = () => {
    if (touchStartX.current !== null && touchEndX.current !== null) {
      const diff = touchStartX.current - touchEndX.current;
      if (Math.abs(diff) > 50) {
        if (diff > 0) {
          next(); // swipe left
        } else {
          prev(); // swipe right
        }
      }
    }
    touchStartX.current = null;
    touchEndX.current = null;
  };

  // Wheel event for desktop
  const handleWheel = (e: React.WheelEvent) => {
    if (Math.abs(e.deltaX) > Math.abs(e.deltaY) && Math.abs(e.deltaX) > 20) {
      if (e.deltaX > 0) {
        next();
      } else {
        prev();
      }
      setIsAutoPlaying(false);
    }
  };

  return (
    <div className="w-full">
      <div
        className="relative w-full max-w-[100vw] h-[650px] mx-auto mt-12 md:mt-10 overflow-clip"
        onWheel={handleWheel}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div className="relative w-full h-[90%]">
          {articles.map((article: any, index: number) => {
            const styles = getStyleForPlayer(index);
            return (
              <div key={`${article.id || article.title}-${index}`} style={styles}>
                <Link href={`/${locale}/news/article/${article.slug}`} className="block w-full h-full focus:outline-none" tabIndex={0}>
                  <div className={`relative h-[650px] rounded-2xl overflow-hidden`}>
                    {article.src ? (
                      <Image
                        src={article.src}
                        alt={article.title}
                        width={index === activeIndex ? 900 : 400}
                        height={600}
                        style={{ height: "650px", objectFit: "cover", objectPosition: "center 100px", width: index === activeIndex ? 900 : 300, borderRadius: '1rem' }}
                        className={`md:h-[650px] p-2 transition-all ease-out`} // keep padding for image
                      />
                    ) : null}
                    {/* Overlay for text readability */}
                    <div className={`absolute inset-0 z-30 ${index === activeIndex ? 'bg-gradient-to-t from-black/80 via-black/30 to-transparent' : 'bg-black/40'} rounded-2xl`}></div>
                    {/* Title & Intro: Responsive, always visible, beautiful */}
                    <div className="absolute left-0 right-0 bottom-0 z-40 p-3 sm:p-6 md:p-8 flex flex-col items-start max-w-full">
                      <h2 className="text-white font-bold text-sm sm:text-xl md:text-xl lg:text-2xl leading-tight line-clamp-2 w-full break-words ps-4 pe-4 mb-3">
                        {article.title}
                      </h2>
                      <p className="text-white/90 text-xs sm:text-sm md:text-sm lg:text-base font-normal line-clamp-2 w-full break-words ps-4 pe-4">
                        {article.intro}
                      </p>
                    </div>
                  </div>
                </Link>
              </div>
            );
          })}
        </div>
        <div className="flex justify-center gap-2">
          {articles.map((_, index: number) => (
            <button
              key={index}
              onClick={() => {
                setActiveIndex(index);
                setIsAutoPlaying(false);
              }}
              className={`w-3 h-3 rounded-full cursor-pointer ${index === activeIndex ? "bg-[#DEB65D]" : "bg-gray-600"
                }`}
            />
          ))}
        </div>

        <div className="absolute bottom-5 z-40 left-1/2 -translate-x-1/2 flex items-center gap-[200px]">
          {/*
          <button
            onClick={prev}
            className="cursor-pointer text-[#989898] px-3 py-1 md:px-4 md:py-2 rounded-lg hover:text-[#EC2028] transition-colors duration-300 text-3xl"
          >
            ❮
          </button>

          <button
            onClick={next}
            className="cursor-pointer text-[#989898] px-3 py-1 md:px-4 md:py-2 rounded-lg hover:text-[#EC2028] transition-colors duration-300 text-3xl"
          >
            ❯
          </button>
          */}
        </div>
      </div>
      {matches.length > 0 && (
        <>
          <div
            ref={containerRef}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseLeave}
            className="w-full max-w-screen py-5 overflow-auto scrollbar-hidden cursor-move">
            <div
              ref={matchesContainerRef}
              className="flex flex-nowrap justify-center items-center gap-4 min-w-fit"
            >
              {matches.map((match, index) => (
                <div
                  key={index}
                  className={`w-[250px] h-[170px] min-w-max bg-white drop-shadow-md flex flex-col select-none gap-3`}
                >
                  <div className="w-full bg-[#272525] border-b-4 border-[#DFB254] p-1">
                    <p className="font-semibold text-center">
                      {capitalizeFirst(formatMatchDate(match.date, locale), locale)}
                    </p>
                  </div>
                  <div className="flex justify-between items-end px-3">
                    <div className="flex-1 flex justify-center">
                      <div className="relative w-[54px] h-[54px] flex items-center justify-center">
                        <Image
                          src={match.home_logo}
                          alt={match.home_team}
                          fill
                          className="object-contain select-none"
                        />
                      </div>
                    </div>
                    <div className="flex flex-col items-center text-center">
                      {(() => {
                        const { time, period } = formatMatchTime(match.time, locale);
                        return <>
                          <p className="text-[#D10128] text-3xl font-bold leading-6">{time}</p>
                          <p className="text-black font-semibold">{capitalizeFirst(period, locale)}</p>
                        </>;
                      })()}
                    </div>
                    <div className="flex-1 flex justify-center">
                      <div className="relative w-[54px] h-[54px] flex items-center justify-center">
                        <Image
                          src={match.away_logo}
                          alt={match.away_team}
                          fill
                          className="object-contain select-none"
                        />
                      </div>
                    </div>
                  </div>
                  <div className="text-black text-center text-lg font-semibold">
                    {capitalizeFirst(match.home_team, locale)} <span className="text-[#D7AB4D]">Vs</span>{' '}
                    {capitalizeFirst(match.away_team, locale)}
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-center items-center gap-4 mb-1 w-full max-w-md mx-auto">
            <Link
              href={`/${locale}/football/schedule`}
              className="px-8 py-2 bg-[#EC2028] rounded-xs cursor-pointer font-semibold mt-2 text-center text-white hover:bg-[#b71c1c] transition flex-1 min-w-0"
              style={{ minWidth: 0 }}
            >
              {t("match_schedule")}
            </Link>
            <Link
              href={`/${locale}/football/results`}
              className="px-8 py-2 bg-[#EC2028] rounded-xs cursor-pointer font-semibold mt-2 text-center text-white hover:bg-[#b71c1c] transition flex-1 min-w-0"
              style={{ minWidth: 0 }}
            >
              {t("نتائج المباريات")}
            </Link>
          </div>
        </>
      )}
    </div>
  );
}
