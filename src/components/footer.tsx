"use client";
import Image from "next/image";
import Link from "next/link";
import translations from "@/lib/locales";
import { usePathname } from "next/navigation";
import { useGlobalData } from "@/context/GlobalDataContext";
import FooterLinks from "./footer/FooterLinks";
import FooterSponsors from "./footer/FooterSponsors";
import FooterCopyright from "./footer/FooterCopyright";
import React from "react";

export default function Footer({ partners }: { partners?: string[] | { name: string; logo: string; url: string }[] }) {
  const { partners: contextPartners, loading: partnersLoading } = useGlobalData();


  const locale = (() => {
    const pathname = usePathname();
    if (!pathname) return "ar";
    const segments = pathname.split("/").filter(Boolean);
    return segments.length > 0 ? segments[0] : "ar";
  })();

  const t = (key: string) => translations[locale]?.[key] || key;

  const sponsors = Array.isArray(partners)
    ? partners.map((p: any) => typeof p === "string" ? { name: p, logo: p, url: "" } : p)
    : (contextPartners.length > 0 ? contextPartners : []);

  return (
    <footer className="w-full flex flex-col gap-4 md:gap-8 text-white bg-[url(/img/smoke-bg.png)] bg-cover" style={{ background: 'linear-gradient(-145deg, #DFB254 0%, #C60428 35%, #7A0515 70%, #0A0A0A 100%)' }}>
      {/* Sponsors Section */}
      {!partnersLoading && sponsors && sponsors.length > 0 && (
        <div className="w-full mx-auto px-2 md:px-4 p-4 mt-[-5%] z-50">
          <FooterSponsors sponsors={sponsors} />
        </div>
      )}
      <div className="flex flex-col-reverse md:flex-row justify-between px-4 md:px-10 lg:px-20">
        <div className="border-t-white border-dashed border-t pt-6 md:pt-10 flex flex-col md:flex-row gap-8 md:gap-12 lg:gap-6">
          <FooterLinks />
        </div>
        <div className="flex flex-col items-center justify-center mb-6 md:mb-0 text-center pt-4">
          <div className="flex justify-center">
            <Link href={`/${locale}`} prefetch={false}>
              <Image
                src="/logo.png"
                alt="Al Ahly logo"
                width={120}
                height={96}
                className="w-[100px] md:w-[150px] h-auto"
              />
            </Link>
          </div>
        </div>
      </div>
      <FooterCopyright locale={locale} t={t} />
    </footer>
  );
}
